# AI Conversational Assistant

A modular AI text generation system focused on achieving English fluency and evolving into a full conversational assistant.

## Project Status
🚧 **Phase 1: English Fluency Foundation** (In Progress)

Current focus: Building a robust text generation model with improved English fluency before adding conversational and assistant capabilities.

## Features
- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **Enhanced LSTM Model**: Improved architecture with attention and regularization
- **Advanced Training**: Learning rate scheduling, early stopping, validation
- **Text Generation**: Multiple sampling strategies (temperature, top-k, nucleus)
- **Configuration Management**: Centralized settings for easy experimentation
- **Evaluation Framework**: Metrics for assessing text quality and fluency

## Quick Start

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ai_assistant

# Install dependencies
pip install -r requirements.txt
```

### Training
```bash
# Train the model
python scripts/train.py

# Monitor training with tensorboard
tensorboard --logdir logs/
```

### Text Generation
```bash
# Generate text
python scripts/generate.py --prompt "Once upon a time" --length 200

# Interactive chat (basic)
python scripts/chat.py
```

## Project Structure
```
ai_assistant/
├── config/           # Configuration files
├── src/             # Source code
│   ├── models/      # Model architectures
│   ├── training/    # Training logic
│   ├── data/        # Data processing
│   ├── inference/   # Text generation
│   └── utils/       # Utilities
├── data/            # Data storage
├── scripts/         # Entry point scripts
└── tests/           # Test suite
```

## Development Roadmap

### Phase 1: English Fluency (Current)
- [x] Project restructuring
- [ ] Enhanced LSTM implementation
- [ ] Advanced training pipeline
- [ ] Text quality evaluation

### Phase 2: Conversational AI
- [ ] Context-aware conversations
- [ ] Intent recognition
- [ ] Response generation

### Phase 3: Assistant Functions
- [ ] Code assistance
- [ ] Task planning
- [ ] Knowledge integration

## Contributing
See `plan.md` for detailed development plans and milestones.

## License
MIT License
