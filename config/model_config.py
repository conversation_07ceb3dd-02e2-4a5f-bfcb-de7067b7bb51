"""Model configuration settings"""

import torch

class ModelConfig:
    """Configuration for LSTM model architecture"""
    
    # Model architecture
    HIDDEN_SIZE = 512  # Increased from 256 for better capacity
    NUM_LAYERS = 3     # Increased from 2 for deeper learning
    DROPOUT = 0.3      # Increased dropout for better regularization
    EMBEDDING_DIM = 256
    
    # Sequence parameters
    SEQUENCE_LENGTH = 128  # Increased from 100 for better context
    
    # Device configuration
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Model saving
    MODEL_SAVE_PATH = "data/models/"
    CHECKPOINT_INTERVAL = 5  # Save every 5 epochs
    
    # Text generation
    DEFAULT_GENERATION_LENGTH = 200
    DEFAULT_TEMPERATURE = 0.8
    TOP_K = 50  # For top-k sampling
    TOP_P = 0.9  # For nucleus sampling
    
    @classmethod
    def get_model_params(cls):
        """Get model parameters as dictionary"""
        return {
            'hidden_size': cls.HIDDEN_SIZE,
            'num_layers': cls.NUM_LAYERS,
            'dropout': cls.DROPOUT,
            'embedding_dim': cls.EMBEDDING_DIM
        }
    
    @classmethod
    def get_generation_params(cls):
        """Get text generation parameters"""
        return {
            'length': cls.DEFAULT_GENERATION_LENGTH,
            'temperature': cls.DEFAULT_TEMPERATURE,
            'top_k': cls.TOP_K,
            'top_p': cls.TOP_P
        }
