"""Training configuration settings"""

class TrainingConfig:
    """Configuration for model training"""
    
    # Training parameters
    BATCH_SIZE = 32        # Reduced for better gradient updates
    LEARNING_RATE = 0.001
    NUM_EPOCHS = 50        # Increased for better convergence
    WEIGHT_DECAY = 1e-5    # L2 regularization
    
    # Learning rate scheduling
    USE_SCHEDULER = True
    SCHEDULER_STEP_SIZE = 10
    SCHEDULER_GAMMA = 0.8
    
    # Gradient clipping
    GRADIENT_CLIP_NORM = 1.0
    
    # Early stopping
    EARLY_STOPPING_PATIENCE = 10
    EARLY_STOPPING_MIN_DELTA = 0.001
    
    # Validation
    VALIDATION_SPLIT = 0.2
    VALIDATION_INTERVAL = 1  # Validate every epoch
    
    # Data loading
    NUM_WORKERS = 4
    SHUFFLE_DATA = True
    
    # Logging
    LOG_INTERVAL = 100  # Log every 100 batches
    SAVE_LOGS = True
    LOG_DIR = "logs/"
    
    # Checkpointing
    SAVE_BEST_MODEL = True
    SAVE_LAST_MODEL = True
    
    @classmethod
    def get_optimizer_params(cls):
        """Get optimizer parameters"""
        return {
            'lr': cls.LEARNING_RATE,
            'weight_decay': cls.WEIGHT_DECAY
        }
    
    @classmethod
    def get_scheduler_params(cls):
        """Get learning rate scheduler parameters"""
        return {
            'step_size': cls.SCHEDULER_STEP_SIZE,
            'gamma': cls.SCHEDULER_GAMMA
        }
