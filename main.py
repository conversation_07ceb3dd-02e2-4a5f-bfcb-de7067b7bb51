import random
import os

import torch
import torch.nn as nn
import torch.optim as optim

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# Check if shakespeare.txt exists
if not os.path.exists('shakespeare.txt'):
    print("Error: shakespeare.txt not found. Please download it first.")
    print("You can download it from: https://raw.githubusercontent.com/karpathy/char-rnn/master/data/tinyshakespeare/input.txt")
    exit(1)

with open('shakespeare.txt', 'r', encoding='utf-8') as f:
    text = f.read().lower()[:50000]  # how many characters to use

print(f"Loaded {len(text)} characters from shakespeare.txt")

chars = sorted(set(text))
vocab_size = len(chars)
print(f"Vocabulary size: {vocab_size}")
print(f"Characters: {''.join(chars[:20])}..." if len(chars) > 20 else f"Characters: {''.join(chars)}")

char2idx = {c: i for i, c in enumerate(chars)}
idx2char = {i: c for i, c in char2idx.items()}

seq_len = 100
step_size = 1
data = [(text[i:i+seq_len], text[i+seq_len]) for i in range(0, len(text)-seq_len, step_size)]
print(f"Created {len(data)} training sequences")

X = torch.tensor([[char2idx[c] for c in seq] for seq, _ in data]).to(device)
y = torch.tensor([char2idx[c] for _, c in data]).to(device)

class CharLSTM(nn.Module):
    def __init__(self, vocab_size, hidden_size, num_layers=2, dropout=0.2):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.embed = nn.Embedding(vocab_size, hidden_size)
        self.lstm = nn.LSTM(hidden_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout if num_layers > 1 else 0)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, vocab_size)

    def forward(self, x, hidden=None):
        x = self.embed(x)
        out, hidden = self.lstm(x, hidden)
        out = self.dropout(out[:, -1, :])  # Apply dropout before final layer
        out = self.fc(out)
        return out, hidden


model = CharLSTM(vocab_size, hidden_size=256, num_layers=2).to(device)
optimizer = optim.Adam(model.parameters(), lr=0.001)
criterion = nn.CrossEntropyLoss()

print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# Training parameters
num_epochs = 20  # Increased for better training
batch_size = 64

for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    num_batches = 0

    for i in range(0, len(X), batch_size):
        x_batch = X[i:i+batch_size]
        y_batch = y[i:i+batch_size]

        if len(x_batch) == 0:
            continue

        optimizer.zero_grad()
        output, _ = model(x_batch)
        loss = criterion(output, y_batch)
        loss.backward()

        # Gradient clipping to prevent exploding gradients
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

        optimizer.step()

        running_loss += loss.item()
        num_batches += 1

    avg_loss = running_loss / num_batches if num_batches > 0 else 0
    print(f'Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.4f}')


def generate_text(model, start_text, length=200, temperature=0.8):
    """Generate text using the trained model with temperature sampling"""
    model.eval()

    # Handle characters not in vocabulary
    filtered_start = ''.join([c for c in start_text.lower() if c in char2idx])
    if not filtered_start:
        filtered_start = random.choice(chars)
        print(f"Warning: Start text contains unknown characters. Using '{filtered_start}' instead.")

    with torch.no_grad():
        input_seq = torch.tensor([[char2idx[c] for c in filtered_start]]).to(device)
        hidden = None

        result = filtered_start

        for _ in range(length):
            output, hidden = model(input_seq, hidden)

            # Apply temperature scaling
            output = output / temperature
            probs = torch.softmax(output, dim=-1).squeeze()

            # Sample from the probability distribution
            next_idx = torch.multinomial(probs, 1).item()
            next_char = idx2char[next_idx]
            result += next_char

            # Update input sequence for next iteration
            input_seq = torch.tensor([[next_idx]]).to(device)

    return result

# Generate some sample text
print("\n" + "="*50)
print("GENERATED TEXT SAMPLES:")
print("="*50)

sample_prompts = [
    'to be or not to be',
    'romeo and juliet',
    'the quick brown fox',
    'once upon a time'
]

for prompt in sample_prompts:
    print(f"\nPrompt: '{prompt}'")
    print("-" * 30)
    generated = generate_text(model, prompt, length=150, temperature=0.8)
    print(generated)

# Save the model
model_path = 'char_lstm.pth'
torch.save({
    'model_state_dict': model.state_dict(),
    'vocab_size': vocab_size,
    'char2idx': char2idx,
    'idx2char': idx2char,
    'hidden_size': 256,
    'num_layers': 2
}, model_path)
print(f"\nModel saved to {model_path}")
