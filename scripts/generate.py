#!/usr/bin/env python3
"""
Text generation script for the AI model
Optimized for Google Colab usage
"""

import os
import sys
import torch
import argparse

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.lstm_model import Enhanced<PERSON><PERSON>
from src.data.preprocessor import TextPreprocessor
from src.inference.generator import TextGenerator
from config.model_config import ModelConfig


def load_model_and_preprocessor(model_path):
    """Load trained model and preprocessor"""
    print(f"Loading model from {model_path}...")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=ModelConfig.DEVICE)
    
    # Recreate preprocessor
    preprocessor = TextPreprocessor()
    preprocessor.char2idx = checkpoint['char2idx']
    preprocessor.idx2char = checkpoint['idx2char']
    preprocessor.vocab_size = checkpoint['vocab_size']
    
    # Recreate model
    model = EnhancedLSTM(preprocessor.vocab_size, ModelConfig())
    model.load_state_dict(checkpoint['model_state_dict'])
    
    print(f"✅ Model loaded successfully!")
    print(f"Vocabulary size: {preprocessor.vocab_size}")
    print(f"Model parameters: {model.get_num_parameters():,}")
    
    return model, preprocessor


def generate_text_interactive():
    """Interactive text generation"""
    # Check if model exists
    model_path = "data/models/final_model_complete.pth"
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("Please train the model first using scripts/train.py")
        return
    
    # Load model
    model, preprocessor = load_model_and_preprocessor(model_path)
    generator = TextGenerator(model, preprocessor)
    
    print("\n🎭 Interactive Text Generation")
    print("="*40)
    print("Commands:")
    print("  - Type any text as a prompt")
    print("  - 'quit' or 'exit' to stop")
    print("  - 'examples' to see example prompts")
    print("-"*40)
    
    while True:
        try:
            prompt = input("\n📝 Enter prompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif prompt.lower() == 'examples':
                show_examples(generator)
                continue
            elif not prompt:
                continue
            
            # Get parameters
            try:
                length = int(input("Length (default 200): ") or "200")
                temperature = float(input("Temperature 0.1-2.0 (default 0.8): ") or "0.8")
            except ValueError:
                length, temperature = 200, 0.8
                print("Using default parameters...")
            
            # Generate text
            print("\n🤖 Generating...")
            generated = generator.generate(
                prompt, 
                length=length, 
                temperature=temperature,
                strategy='temperature'
            )
            
            print(f"\n📖 Generated text:")
            print("-" * 40)
            print(generated)
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def show_examples(generator):
    """Show example generations"""
    examples = [
        "to be or not to be",
        "once upon a time",
        "the quick brown fox",
        "in a hole in the ground",
        "it was the best of times"
    ]
    
    print("\n🎯 Example Generations:")
    print("="*40)
    
    for prompt in examples:
        generated = generator.generate(prompt, length=150, temperature=0.8)
        print(f"\nPrompt: '{prompt}'")
        print(f"Generated: {generated}")
        print("-" * 30)


def generate_batch(prompts, length=200, temperature=0.8):
    """Generate text for multiple prompts"""
    model_path = "data/models/final_model_complete.pth"
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return
    
    model, preprocessor = load_model_and_preprocessor(model_path)
    generator = TextGenerator(model, preprocessor)
    
    print(f"\n🎭 Batch Generation (Length: {length}, Temperature: {temperature})")
    print("="*60)
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n[{i}/{len(prompts)}] Prompt: '{prompt}'")
        generated = generator.generate(prompt, length=length, temperature=temperature)
        print(f"Generated: {generated}")
        print("-" * 50)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate text using trained AI model')
    parser.add_argument('--prompt', type=str, help='Text prompt for generation')
    parser.add_argument('--length', type=int, default=200, help='Length of generated text')
    parser.add_argument('--temperature', type=float, default=0.8, help='Sampling temperature')
    parser.add_argument('--interactive', action='store_true', help='Interactive mode')
    parser.add_argument('--examples', action='store_true', help='Show examples')
    
    args = parser.parse_args()
    
    if args.interactive or (not args.prompt and not args.examples):
        generate_text_interactive()
    elif args.examples:
        model_path = "data/models/final_model_complete.pth"
        if os.path.exists(model_path):
            model, preprocessor = load_model_and_preprocessor(model_path)
            generator = TextGenerator(model, preprocessor)
            show_examples(generator)
        else:
            print("❌ Model not found. Please train first.")
    elif args.prompt:
        generate_batch([args.prompt], args.length, args.temperature)


if __name__ == "__main__":
    main()
