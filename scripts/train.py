#!/usr/bin/env python3
"""
Main training script for the AI text generation model
Optimized for Google Colab usage
"""

import os
import sys
import torch
from torch.utils.data import DataLoader

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.lstm_model import EnhancedLSTM
from src.data.preprocessor import TextPreprocessor, TextDataset, load_text_data, create_train_val_split
from src.training.trainer import Trainer
from src.utils.helpers import set_seed, get_device_info, plot_training_history
from src.utils.logger import TrainingLogger
from config.model_config import ModelConfig
from config.training_config import TrainingConfig


def main():
    """Main training function"""
    print("🚀 Starting AI Text Generation Training")
    print("="*50)
    
    # Set seed for reproducibility
    set_seed(42)
    
    # Print device info
    device_info = get_device_info()
    print(f"Device: {device_info['device']}")
    if device_info['cuda_available']:
        print(f"GPU: {device_info.get('gpu_name', 'Unknown')}")
        if device_info.get('gpu_memory'):
            print(f"GPU Memory: {device_info['gpu_memory'] / 1e9:.1f} GB")
    
    # Load and preprocess data
    print("\n📚 Loading and preprocessing data...")
    
    # Check if data exists
    data_path = "data/raw/shakespeare.txt"
    if not os.path.exists(data_path):
        print(f"❌ Data file not found: {data_path}")
        print("Please upload shakespeare.txt to data/raw/ directory")
        print("You can download it from: https://raw.githubusercontent.com/karpathy/char-rnn/master/data/tinyshakespeare/input.txt")
        return
    
    # Load text data
    texts = load_text_data(data_path)
    
    # Create preprocessor and build vocabulary
    preprocessor = TextPreprocessor(min_freq=2, max_vocab_size=1000)
    vocab = preprocessor.build_vocabulary(texts)
    
    # Save vocabulary
    vocab_path = "data/processed/vocabulary.pkl"
    preprocessor.save_vocabulary(vocab_path)
    
    # Create dataset
    print("\n🔄 Creating training dataset...")
    dataset = TextDataset(texts, preprocessor, ModelConfig.SEQUENCE_LENGTH)
    
    # Split into train/validation
    train_dataset, val_dataset = create_train_val_split(
        dataset, TrainingConfig.VALIDATION_SPLIT
    )
    
    # Create model
    print("\n🧠 Creating model...")
    model = EnhancedLSTM(preprocessor.vocab_size, ModelConfig())
    print(f"Model parameters: {model.get_num_parameters():,}")
    
    # Create trainer
    print("\n🏋️ Setting up trainer...")
    trainer = Trainer(model, train_dataset, val_dataset, TrainingConfig())
    
    # Start training
    print("\n🚂 Starting training...")
    history = trainer.train()
    
    # Plot training history
    print("\n📊 Plotting training history...")
    plot_training_history(history, "logs/training_history.png")
    
    # Save final model with metadata
    print("\n💾 Saving final model...")
    final_model_path = "data/models/final_model_complete.pth"
    os.makedirs(os.path.dirname(final_model_path), exist_ok=True)
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': ModelConfig(),
        'vocab_size': preprocessor.vocab_size,
        'char2idx': preprocessor.char2idx,
        'idx2char': preprocessor.idx2char,
        'training_history': history,
        'model_class': 'EnhancedLSTM'
    }, final_model_path)
    
    print(f"✅ Training completed! Model saved to {final_model_path}")
    
    # Quick generation test
    print("\n🎭 Testing text generation...")
    from src.inference.generator import TextGenerator
    
    generator = TextGenerator(model, preprocessor)
    test_prompts = ["to be or not to be", "once upon a time", "the quick brown"]
    
    for prompt in test_prompts:
        generated = generator.generate(prompt, length=100, temperature=0.8)
        print(f"\nPrompt: '{prompt}'")
        print(f"Generated: {generated}")
    
    print("\n🎉 All done! You can now use the model for text generation.")


if __name__ == "__main__":
    main()
