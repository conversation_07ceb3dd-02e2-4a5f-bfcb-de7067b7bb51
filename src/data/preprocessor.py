"""Data preprocessing utilities for text generation"""

import os
import re
import pickle
import torch
from torch.utils.data import Dataset
from collections import Counter
from config.model_config import ModelConfig


class TextPreprocessor:
    """Text preprocessing and vocabulary management"""
    
    def __init__(self, min_freq=2, max_vocab_size=10000):
        self.min_freq = min_freq
        self.max_vocab_size = max_vocab_size
        self.char2idx = {}
        self.idx2char = {}
        self.vocab_size = 0
        
        # Special tokens
        self.PAD_TOKEN = '<PAD>'
        self.UNK_TOKEN = '<UNK>'
        self.START_TOKEN = '<START>'
        self.END_TOKEN = '<END>'
        
        self.special_tokens = [
            self.PAD_TOKEN, self.UNK_TOKEN, 
            self.START_TOKEN, self.END_TOKEN
        ]
    
    def clean_text(self, text):
        """Clean and normalize text"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?;:\'"()-]', '', text)
        
        # Normalize quotes
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r'[''']', "'", text)
        
        return text.strip()
    
    def build_vocabulary(self, texts):
        """Build character vocabulary from texts"""
        print("Building vocabulary...")
        
        # Combine all texts
        combined_text = ' '.join(texts)
        cleaned_text = self.clean_text(combined_text)
        
        # Count character frequencies
        char_counts = Counter(cleaned_text)
        
        # Start with special tokens
        vocab = self.special_tokens.copy()
        
        # Add characters based on frequency
        for char, count in char_counts.most_common():
            if count >= self.min_freq and len(vocab) < self.max_vocab_size:
                if char not in vocab:
                    vocab.append(char)
        
        # Create mappings
        self.char2idx = {char: idx for idx, char in enumerate(vocab)}
        self.idx2char = {idx: char for char, idx in self.char2idx.items()}
        self.vocab_size = len(vocab)
        
        print(f"Vocabulary size: {self.vocab_size}")
        print(f"Characters: {''.join(vocab[:50])}..." if len(vocab) > 50 else f"Characters: {''.join(vocab)}")
        
        return vocab
    
    def encode_text(self, text):
        """Encode text to indices"""
        cleaned_text = self.clean_text(text)
        return [
            self.char2idx.get(char, self.char2idx[self.UNK_TOKEN]) 
            for char in cleaned_text
        ]
    
    def decode_indices(self, indices):
        """Decode indices to text"""
        return ''.join([
            self.idx2char.get(idx, self.UNK_TOKEN) 
            for idx in indices
        ])
    
    def save_vocabulary(self, path):
        """Save vocabulary to file"""
        vocab_data = {
            'char2idx': self.char2idx,
            'idx2char': self.idx2char,
            'vocab_size': self.vocab_size,
            'special_tokens': self.special_tokens
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'wb') as f:
            pickle.dump(vocab_data, f)
        print(f"Vocabulary saved to {path}")
    
    def load_vocabulary(self, path):
        """Load vocabulary from file"""
        with open(path, 'rb') as f:
            vocab_data = pickle.load(f)
        
        self.char2idx = vocab_data['char2idx']
        self.idx2char = vocab_data['idx2char']
        self.vocab_size = vocab_data['vocab_size']
        self.special_tokens = vocab_data['special_tokens']
        
        print(f"Vocabulary loaded from {path}")
        print(f"Vocabulary size: {self.vocab_size}")


class TextDataset(Dataset):
    """Dataset for text generation training"""
    
    def __init__(self, texts, preprocessor, sequence_length=None):
        if sequence_length is None:
            sequence_length = ModelConfig.SEQUENCE_LENGTH
            
        self.sequence_length = sequence_length
        self.preprocessor = preprocessor
        
        # Encode all texts
        self.encoded_texts = []
        for text in texts:
            encoded = preprocessor.encode_text(text)
            self.encoded_texts.extend(encoded)
        
        print(f"Total encoded characters: {len(self.encoded_texts)}")
        
        # Create sequences
        self.sequences = []
        for i in range(len(self.encoded_texts) - sequence_length):
            input_seq = self.encoded_texts[i:i + sequence_length]
            target = self.encoded_texts[i + sequence_length]
            self.sequences.append((input_seq, target))
        
        print(f"Created {len(self.sequences)} training sequences")
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        input_seq, target = self.sequences[idx]
        return torch.tensor(input_seq, dtype=torch.long), torch.tensor(target, dtype=torch.long)


def load_text_data(data_path):
    """Load text data from file or directory"""
    texts = []
    
    if os.path.isfile(data_path):
        # Single file
        with open(data_path, 'r', encoding='utf-8') as f:
            texts.append(f.read())
    elif os.path.isdir(data_path):
        # Directory of text files
        for filename in os.listdir(data_path):
            if filename.endswith('.txt'):
                filepath = os.path.join(data_path, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    texts.append(f.read())
    else:
        raise ValueError(f"Data path {data_path} not found")
    
    print(f"Loaded {len(texts)} text files")
    total_chars = sum(len(text) for text in texts)
    print(f"Total characters: {total_chars:,}")
    
    return texts


def create_train_val_split(dataset, val_split=0.2):
    """Split dataset into training and validation sets"""
    val_size = int(len(dataset) * val_split)
    train_size = len(dataset) - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    return train_dataset, val_dataset
