"""Text generation module with advanced sampling strategies"""

import torch
import torch.nn.functional as F
import random
from typing import Optional, List
from config.model_config import ModelConfig


class TextGenerator:
    """Advanced text generator with multiple sampling strategies"""
    
    def __init__(self, model, preprocessor, config=None):
        self.model = model
        self.preprocessor = preprocessor
        self.config = config or ModelConfig()
        self.device = self.config.DEVICE
        
        self.model.to(self.device)
        self.model.eval()
    
    def generate(self, prompt: str, length: int = None, temperature: float = None,
                 top_k: int = None, top_p: float = None, strategy: str = 'temperature') -> str:
        """
        Generate text using various sampling strategies
        
        Args:
            prompt: Starting text
            length: Number of characters to generate
            temperature: Sampling temperature (higher = more random)
            top_k: Top-k sampling parameter
            top_p: Nucleus sampling parameter
            strategy: Sampling strategy ('temperature', 'top_k', 'top_p', 'greedy')
        
        Returns:
            Generated text
        """
        if length is None:
            length = self.config.DEFAULT_GENERATION_LENGTH
        if temperature is None:
            temperature = self.config.DEFAULT_TEMPERATURE
        if top_k is None:
            top_k = self.config.TOP_K
        if top_p is None:
            top_p = self.config.TOP_P
        
        # Prepare input
        filtered_prompt = self._filter_prompt(prompt)
        if not filtered_prompt:
            filtered_prompt = random.choice(list(self.preprocessor.char2idx.keys()))
        
        with torch.no_grad():
            input_seq = torch.tensor(
                [self.preprocessor.encode_text(filtered_prompt)],
                dtype=torch.long
            ).to(self.device)
            
            hidden = None
            result = filtered_prompt
            
            for _ in range(length):
                # Get model output
                output, hidden = self.model(input_seq, hidden)
                
                # Apply sampling strategy
                if strategy == 'greedy':
                    next_idx = self._greedy_sampling(output)
                elif strategy == 'temperature':
                    next_idx = self._temperature_sampling(output, temperature)
                elif strategy == 'top_k':
                    next_idx = self._top_k_sampling(output, top_k, temperature)
                elif strategy == 'top_p':
                    next_idx = self._top_p_sampling(output, top_p, temperature)
                else:
                    raise ValueError(f"Unknown sampling strategy: {strategy}")
                
                # Convert to character and append
                next_char = self.preprocessor.idx2char.get(next_idx, '')
                if next_char:
                    result += next_char
                
                # Update input for next iteration
                input_seq = torch.tensor([[next_idx]], dtype=torch.long).to(self.device)
        
        return result
    
    def _filter_prompt(self, prompt: str) -> str:
        """Filter prompt to only include known characters"""
        cleaned_prompt = self.preprocessor.clean_text(prompt)
        filtered_chars = [
            c for c in cleaned_prompt 
            if c in self.preprocessor.char2idx
        ]
        return ''.join(filtered_chars)
    
    def _greedy_sampling(self, logits: torch.Tensor) -> int:
        """Greedy sampling - always pick the most likely token"""
        return torch.argmax(logits, dim=-1).item()
    
    def _temperature_sampling(self, logits: torch.Tensor, temperature: float) -> int:
        """Temperature sampling"""
        if temperature <= 0:
            return self._greedy_sampling(logits)
        
        # Apply temperature
        logits = logits / temperature
        probs = F.softmax(logits, dim=-1)
        
        # Sample from distribution
        return torch.multinomial(probs, 1).item()
    
    def _top_k_sampling(self, logits: torch.Tensor, top_k: int, temperature: float) -> int:
        """Top-k sampling"""
        if top_k <= 0:
            return self._temperature_sampling(logits, temperature)
        
        # Get top-k values and indices
        top_k = min(top_k, logits.size(-1))
        top_k_logits, top_k_indices = torch.topk(logits, top_k)
        
        # Apply temperature to top-k logits
        if temperature > 0:
            top_k_logits = top_k_logits / temperature
        
        # Convert to probabilities
        top_k_probs = F.softmax(top_k_logits, dim=-1)
        
        # Sample from top-k distribution
        sampled_idx = torch.multinomial(top_k_probs, 1).item()
        return top_k_indices[sampled_idx].item()
    
    def _top_p_sampling(self, logits: torch.Tensor, top_p: float, temperature: float) -> int:
        """Nucleus (top-p) sampling"""
        if top_p <= 0 or top_p >= 1:
            return self._temperature_sampling(logits, temperature)
        
        # Apply temperature
        if temperature > 0:
            logits = logits / temperature
        
        # Convert to probabilities and sort
        probs = F.softmax(logits, dim=-1)
        sorted_probs, sorted_indices = torch.sort(probs, descending=True)
        
        # Calculate cumulative probabilities
        cumulative_probs = torch.cumsum(sorted_probs, dim=-1)
        
        # Find cutoff point
        cutoff_idx = torch.where(cumulative_probs > top_p)[0]
        if len(cutoff_idx) > 0:
            cutoff_idx = cutoff_idx[0].item()
            sorted_probs[cutoff_idx:] = 0
        
        # Renormalize
        sorted_probs = sorted_probs / sorted_probs.sum()
        
        # Sample from nucleus
        sampled_idx = torch.multinomial(sorted_probs, 1).item()
        return sorted_indices[sampled_idx].item()
    
    def generate_multiple(self, prompts: List[str], **kwargs) -> List[str]:
        """Generate text for multiple prompts"""
        results = []
        for prompt in prompts:
            result = self.generate(prompt, **kwargs)
            results.append(result)
        return results
    
    def interactive_generation(self):
        """Interactive text generation session"""
        print("Interactive Text Generation")
        print("Commands: 'quit' to exit, 'help' for options")
        print("-" * 40)
        
        while True:
            try:
                prompt = input("\nEnter prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    break
                elif prompt.lower() == 'help':
                    self._print_help()
                    continue
                elif not prompt:
                    continue
                
                # Get generation parameters
                length = self._get_int_input("Length (default 200): ", 200)
                temperature = self._get_float_input("Temperature (default 0.8): ", 0.8)
                strategy = self._get_strategy_input()
                
                # Generate text
                print("\nGenerating...")
                generated = self.generate(
                    prompt, length=length, temperature=temperature, strategy=strategy
                )
                
                print(f"\nGenerated text:\n{generated}")
                
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def _print_help(self):
        """Print help information"""
        print("\nGeneration Options:")
        print("- Length: Number of characters to generate")
        print("- Temperature: Randomness (0.1=conservative, 1.0=balanced, 2.0=creative)")
        print("- Strategy: greedy, temperature, top_k, top_p")
    
    def _get_int_input(self, prompt: str, default: int) -> int:
        """Get integer input with default"""
        try:
            value = input(prompt).strip()
            return int(value) if value else default
        except ValueError:
            return default
    
    def _get_float_input(self, prompt: str, default: float) -> float:
        """Get float input with default"""
        try:
            value = input(prompt).strip()
            return float(value) if value else default
        except ValueError:
            return default
    
    def _get_strategy_input(self) -> str:
        """Get sampling strategy input"""
        strategies = ['temperature', 'top_k', 'top_p', 'greedy']
        strategy = input(f"Strategy {strategies} (default temperature): ").strip().lower()
        return strategy if strategy in strategies else 'temperature'
