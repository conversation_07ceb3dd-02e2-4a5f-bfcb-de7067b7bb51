"""Enhanced LSTM model for text generation with improved architecture"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from config.model_config import ModelConfig


class EnhancedLSTM(nn.Module):
    """Enhanced LSTM model with attention and improved architecture"""
    
    def __init__(self, vocab_size, config=None):
        super().__init__()
        
        if config is None:
            config = ModelConfig()
        
        self.vocab_size = vocab_size
        self.hidden_size = config.HIDDEN_SIZE
        self.num_layers = config.NUM_LAYERS
        self.dropout = config.DROPOUT
        self.embedding_dim = config.EMBEDDING_DIM
        
        # Embedding layer
        self.embedding = nn.Embedding(vocab_size, self.embedding_dim)
        
        # LSTM layers
        self.lstm = nn.LSTM(
            self.embedding_dim,
            self.hidden_size,
            self.num_layers,
            batch_first=True,
            dropout=self.dropout if self.num_layers > 1 else 0,
            bidirectional=False
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            self.hidden_size,
            num_heads=8,
            dropout=self.dropout,
            batch_first=True
        )
        
        # Layer normalization
        self.layer_norm1 = nn.LayerNorm(self.hidden_size)
        self.layer_norm2 = nn.LayerNorm(self.hidden_size)
        
        # Feed-forward network
        self.feed_forward = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size * 4),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size * 4, self.hidden_size),
            nn.Dropout(self.dropout)
        )
        
        # Output projection
        self.output_projection = nn.Linear(self.hidden_size, vocab_size)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'lstm' in name:
                    # Xavier initialization for LSTM weights
                    nn.init.xavier_uniform_(param)
                else:
                    # Normal initialization for other weights
                    nn.init.normal_(param, mean=0.0, std=0.02)
            elif 'bias' in name:
                nn.init.constant_(param, 0.0)
    
    def forward(self, x, hidden=None, return_attention=False):
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, seq_len)
            hidden: Hidden state tuple (h_0, c_0)
            return_attention: Whether to return attention weights
        
        Returns:
            output: Logits of shape (batch_size, vocab_size)
            hidden: Updated hidden state
            attention_weights: Attention weights (if return_attention=True)
        """
        batch_size, seq_len = x.shape
        
        # Embedding
        embedded = self.embedding(x)  # (batch_size, seq_len, embedding_dim)
        
        # LSTM
        lstm_out, hidden = self.lstm(embedded, hidden)  # (batch_size, seq_len, hidden_size)
        
        # Self-attention
        attn_out, attn_weights = self.attention(
            lstm_out, lstm_out, lstm_out
        )  # (batch_size, seq_len, hidden_size)
        
        # Residual connection and layer norm
        lstm_out = self.layer_norm1(lstm_out + attn_out)
        
        # Feed-forward network
        ff_out = self.feed_forward(lstm_out)
        
        # Residual connection and layer norm
        output = self.layer_norm2(lstm_out + ff_out)
        
        # Use the last time step for prediction
        output = output[:, -1, :]  # (batch_size, hidden_size)
        
        # Output projection
        logits = self.output_projection(output)  # (batch_size, vocab_size)
        
        if return_attention:
            return logits, hidden, attn_weights
        return logits, hidden
    
    def init_hidden(self, batch_size, device):
        """Initialize hidden state"""
        h_0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(device)
        c_0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(device)
        return (h_0, c_0)
    
    def get_num_parameters(self):
        """Get total number of parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)


class SimpleLSTM(nn.Module):
    """Simplified LSTM model for comparison"""
    
    def __init__(self, vocab_size, hidden_size=256, num_layers=2, dropout=0.2):
        super().__init__()
        
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.embedding = nn.Embedding(vocab_size, hidden_size)
        self.lstm = nn.LSTM(
            hidden_size, hidden_size, num_layers,
            batch_first=True, dropout=dropout if num_layers > 1 else 0
        )
        self.dropout = nn.Dropout(dropout)
        self.output_projection = nn.Linear(hidden_size, vocab_size)
    
    def forward(self, x, hidden=None):
        embedded = self.embedding(x)
        lstm_out, hidden = self.lstm(embedded, hidden)
        output = self.dropout(lstm_out[:, -1, :])
        logits = self.output_projection(output)
        return logits, hidden
    
    def init_hidden(self, batch_size, device):
        h_0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(device)
        c_0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(device)
        return (h_0, c_0)
