"""Evaluation module for text generation models"""

import torch
import torch.nn.functional as F
import numpy as np
from collections import Counter
import re
from typing import List, Dict, Tuple
from src.utils.helpers import calculate_perplexity


class TextEvaluator:
    """Comprehensive text generation evaluator"""
    
    def __init__(self, model, preprocessor, device=None):
        self.model = model
        self.preprocessor = preprocessor
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        self.model.to(self.device)
        self.model.eval()
    
    def evaluate_perplexity(self, test_data, batch_size=32):
        """Calculate perplexity on test data"""
        total_loss = 0
        total_samples = 0
        
        self.model.eval()
        with torch.no_grad():
            for i in range(0, len(test_data), batch_size):
                batch = test_data[i:i + batch_size]
                if len(batch) == 0:
                    continue
                
                inputs = torch.stack([item[0] for item in batch]).to(self.device)
                targets = torch.stack([item[1] for item in batch]).to(self.device)
                
                outputs, _ = self.model(inputs)
                loss = F.cross_entropy(outputs, targets, reduction='sum')
                
                total_loss += loss.item()
                total_samples += len(batch)
        
        avg_loss = total_loss / total_samples
        perplexity = calculate_perplexity(avg_loss)
        
        return {
            'perplexity': perplexity,
            'avg_loss': avg_loss,
            'total_samples': total_samples
        }
    
    def evaluate_text_quality(self, prompts: List[str], length: int = 200, 
                            num_samples: int = 5) -> Dict:
        """Evaluate generated text quality using multiple metrics"""
        generated_texts = []
        
        # Generate multiple samples for each prompt
        for prompt in prompts:
            for _ in range(num_samples):
                from src.inference.generator import TextGenerator
                generator = TextGenerator(self.model, self.preprocessor)
                text = generator.generate(prompt, length=length)
                generated_texts.append(text)
        
        # Calculate metrics
        metrics = {
            'readability': self._calculate_readability(generated_texts),
            'diversity': self._calculate_diversity(generated_texts),
            'coherence': self._calculate_coherence(generated_texts),
            'grammar': self._calculate_grammar_score(generated_texts),
            'repetition': self._calculate_repetition_score(generated_texts)
        }
        
        return metrics
    
    def _calculate_readability(self, texts: List[str]) -> float:
        """Calculate readability score based on sentence structure"""
        scores = []
        
        for text in texts:
            # Count sentences, words, and characters
            sentences = len(re.findall(r'[.!?]+', text))
            words = len(text.split())
            chars = len(text)
            
            if sentences == 0 or words == 0:
                scores.append(0.0)
                continue
            
            # Simple readability metric
            avg_sentence_length = words / sentences
            avg_word_length = chars / words
            
            # Normalize to 0-1 scale (optimal ranges: 15-20 words/sentence, 4-6 chars/word)
            sentence_score = max(0, 1 - abs(avg_sentence_length - 17.5) / 17.5)
            word_score = max(0, 1 - abs(avg_word_length - 5) / 5)
            
            scores.append((sentence_score + word_score) / 2)
        
        return np.mean(scores) if scores else 0.0
    
    def _calculate_diversity(self, texts: List[str]) -> float:
        """Calculate lexical diversity (unique words / total words)"""
        all_words = []
        for text in texts:
            words = re.findall(r'\b\w+\b', text.lower())
            all_words.extend(words)
        
        if not all_words:
            return 0.0
        
        unique_words = len(set(all_words))
        total_words = len(all_words)
        
        return unique_words / total_words
    
    def _calculate_coherence(self, texts: List[str]) -> float:
        """Calculate coherence based on topic consistency"""
        scores = []
        
        for text in texts:
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if len(sentences) < 2:
                scores.append(0.5)  # Neutral score for very short texts
                continue
            
            # Simple coherence metric: word overlap between consecutive sentences
            overlaps = []
            for i in range(len(sentences) - 1):
                words1 = set(re.findall(r'\b\w+\b', sentences[i].lower()))
                words2 = set(re.findall(r'\b\w+\b', sentences[i + 1].lower()))
                
                if not words1 or not words2:
                    overlaps.append(0)
                    continue
                
                overlap = len(words1.intersection(words2))
                total = len(words1.union(words2))
                overlaps.append(overlap / total if total > 0 else 0)
            
            scores.append(np.mean(overlaps) if overlaps else 0.0)
        
        return np.mean(scores) if scores else 0.0
    
    def _calculate_grammar_score(self, texts: List[str]) -> float:
        """Simple grammar score based on basic patterns"""
        scores = []
        
        for text in texts:
            score = 0.0
            total_checks = 0
            
            # Check for proper capitalization after periods
            sentences = re.split(r'[.!?]+', text)
            for sentence in sentences:
                sentence = sentence.strip()
                if sentence:
                    total_checks += 1
                    if sentence[0].isupper():
                        score += 1
            
            # Check for balanced quotes
            quote_count = text.count('"')
            if quote_count % 2 == 0:
                score += 1
            total_checks += 1
            
            # Check for reasonable punctuation
            words = len(text.split())
            punctuation = len(re.findall(r'[.!?,:;]', text))
            if words > 0:
                punct_ratio = punctuation / words
                if 0.05 <= punct_ratio <= 0.3:  # Reasonable punctuation ratio
                    score += 1
                total_checks += 1
            
            scores.append(score / total_checks if total_checks > 0 else 0.0)
        
        return np.mean(scores) if scores else 0.0
    
    def _calculate_repetition_score(self, texts: List[str]) -> float:
        """Calculate repetition score (lower is better)"""
        scores = []
        
        for text in texts:
            words = re.findall(r'\b\w+\b', text.lower())
            if len(words) < 10:
                scores.append(0.0)
                continue
            
            # Check for immediate repetitions (same word repeated)
            immediate_reps = 0
            for i in range(len(words) - 1):
                if words[i] == words[i + 1]:
                    immediate_reps += 1
            
            # Check for phrase repetitions (3+ word sequences)
            phrase_reps = 0
            for i in range(len(words) - 5):
                phrase = ' '.join(words[i:i + 3])
                remaining_text = ' '.join(words[i + 3:])
                if phrase in remaining_text:
                    phrase_reps += 1
            
            # Calculate repetition score (0 = no repetition, 1 = high repetition)
            rep_score = (immediate_reps + phrase_reps) / len(words)
            scores.append(min(1.0, rep_score))
        
        # Return inverse (so higher is better)
        return 1.0 - np.mean(scores) if scores else 1.0
    
    def generate_evaluation_report(self, test_data, prompts: List[str]) -> Dict:
        """Generate comprehensive evaluation report"""
        print("Generating evaluation report...")
        
        # Perplexity evaluation
        perplexity_results = self.evaluate_perplexity(test_data)
        
        # Text quality evaluation
        quality_results = self.evaluate_text_quality(prompts)
        
        # Combine results
        report = {
            'perplexity': perplexity_results,
            'text_quality': quality_results,
            'overall_score': self._calculate_overall_score(perplexity_results, quality_results)
        }
        
        return report
    
    def _calculate_overall_score(self, perplexity_results: Dict, quality_results: Dict) -> float:
        """Calculate overall model score"""
        # Normalize perplexity (lower is better, cap at 100)
        perplexity = min(perplexity_results['perplexity'], 100)
        perplexity_score = max(0, 1 - perplexity / 100)
        
        # Average quality metrics
        quality_score = np.mean([
            quality_results['readability'],
            quality_results['diversity'],
            quality_results['coherence'],
            quality_results['grammar'],
            quality_results['repetition']
        ])
        
        # Weighted combination (60% quality, 40% perplexity)
        overall_score = 0.6 * quality_score + 0.4 * perplexity_score
        
        return overall_score
    
    def print_evaluation_report(self, report: Dict):
        """Print formatted evaluation report"""
        print("\n" + "="*60)
        print("MODEL EVALUATION REPORT")
        print("="*60)
        
        # Perplexity results
        perp = report['perplexity']
        print(f"\nPerplexity Metrics:")
        print(f"  Perplexity: {perp['perplexity']:.2f}")
        print(f"  Average Loss: {perp['avg_loss']:.4f}")
        print(f"  Test Samples: {perp['total_samples']:,}")
        
        # Text quality results
        quality = report['text_quality']
        print(f"\nText Quality Metrics:")
        print(f"  Readability: {quality['readability']:.3f}")
        print(f"  Diversity: {quality['diversity']:.3f}")
        print(f"  Coherence: {quality['coherence']:.3f}")
        print(f"  Grammar: {quality['grammar']:.3f}")
        print(f"  Repetition: {quality['repetition']:.3f}")
        
        # Overall score
        print(f"\nOverall Score: {report['overall_score']:.3f}")
        
        # Interpretation
        score = report['overall_score']
        if score >= 0.8:
            interpretation = "Excellent"
        elif score >= 0.6:
            interpretation = "Good"
        elif score >= 0.4:
            interpretation = "Fair"
        else:
            interpretation = "Needs Improvement"
        
        print(f"Model Quality: {interpretation}")
        print("="*60)
