"""Training module for text generation models"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

from config.training_config import TrainingConfig
from config.model_config import ModelConfig


class Trainer:
    """Model trainer with advanced features"""
    
    def __init__(self, model, train_dataset, val_dataset=None, config=None):
        self.model = model
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        
        if config is None:
            config = TrainingConfig()
        self.config = config
        
        self.device = ModelConfig.DEVICE
        self.model.to(self.device)
        
        # Setup optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            **config.get_optimizer_params()
        )
        
        # Setup learning rate scheduler
        if config.USE_SCHEDULER:
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                **config.get_scheduler_params()
            )
        else:
            self.scheduler = None
        
        # Setup loss function
        self.criterion = nn.CrossEntropyLoss()
        
        # Setup data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=config.BATCH_SIZE,
            shuffle=config.SHUFFLE_DATA,
            num_workers=config.NUM_WORKERS
        )
        
        if val_dataset:
            self.val_loader = DataLoader(
                val_dataset,
                batch_size=config.BATCH_SIZE,
                shuffle=False,
                num_workers=config.NUM_WORKERS
            )
        
        # Setup logging
        if config.SAVE_LOGS:
            os.makedirs(config.LOG_DIR, exist_ok=True)
            self.writer = SummaryWriter(config.LOG_DIR)
        else:
            self.writer = None
        
        # Training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
    
    def train_epoch(self):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch + 1}')
        
        for batch_idx, (inputs, targets) in enumerate(progress_bar):
            inputs, targets = inputs.to(self.device), targets.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs, _ = self.model(inputs)
            loss = self.criterion(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config.GRADIENT_CLIP_NORM > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config.GRADIENT_CLIP_NORM
                )
            
            self.optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            avg_loss = total_loss / num_batches
            progress_bar.set_postfix({'loss': f'{avg_loss:.4f}'})
            
            # Log batch metrics
            if (batch_idx + 1) % self.config.LOG_INTERVAL == 0 and self.writer:
                global_step = self.current_epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Loss/Train_Batch', loss.item(), global_step)
        
        return total_loss / num_batches
    
    def validate(self):
        """Validate the model"""
        if not self.val_dataset:
            return None
        
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for inputs, targets in self.val_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)
                
                outputs, _ = self.model(inputs)
                loss = self.criterion(outputs, targets)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def save_checkpoint(self, filepath, is_best=False):
        """Save model checkpoint"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        torch.save(checkpoint, filepath)
        
        if is_best:
            best_path = filepath.replace('.pth', '_best.pth')
            torch.save(checkpoint, best_path)
    
    def load_checkpoint(self, filepath):
        """Load model checkpoint"""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.current_epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['best_val_loss']
        self.training_history = checkpoint['training_history']
        
        if self.scheduler and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    def train(self, num_epochs=None):
        """Main training loop"""
        if num_epochs is None:
            num_epochs = self.config.NUM_EPOCHS
        
        print(f"Starting training for {num_epochs} epochs")
        print(f"Device: {self.device}")
        print(f"Model parameters: {self.model.get_num_parameters():,}")
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            
            # Train
            train_loss = self.train_epoch()
            self.training_history['train_loss'].append(train_loss)
            
            # Validate
            val_loss = self.validate()
            if val_loss is not None:
                self.training_history['val_loss'].append(val_loss)
            
            # Learning rate scheduling
            if self.scheduler:
                self.scheduler.step()
                current_lr = self.scheduler.get_last_lr()[0]
                self.training_history['learning_rate'].append(current_lr)
            
            # Logging
            print(f'Epoch {epoch + 1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss:.4f}')
            if val_loss is not None:
                print(f'  Val Loss: {val_loss:.4f}')
            
            if self.writer:
                self.writer.add_scalar('Loss/Train_Epoch', train_loss, epoch)
                if val_loss is not None:
                    self.writer.add_scalar('Loss/Val_Epoch', val_loss, epoch)
                if self.scheduler:
                    self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            # Save checkpoints
            if (epoch + 1) % ModelConfig.CHECKPOINT_INTERVAL == 0:
                checkpoint_path = os.path.join(
                    ModelConfig.MODEL_SAVE_PATH,
                    f'checkpoint_epoch_{epoch + 1}.pth'
                )
                self.save_checkpoint(checkpoint_path)
            
            # Early stopping and best model saving
            if val_loss is not None:
                if val_loss < self.best_val_loss - self.config.EARLY_STOPPING_MIN_DELTA:
                    self.best_val_loss = val_loss
                    self.patience_counter = 0
                    
                    if self.config.SAVE_BEST_MODEL:
                        best_path = os.path.join(
                            ModelConfig.MODEL_SAVE_PATH,
                            'best_model.pth'
                        )
                        self.save_checkpoint(best_path, is_best=True)
                else:
                    self.patience_counter += 1
                
                if self.patience_counter >= self.config.EARLY_STOPPING_PATIENCE:
                    print(f"Early stopping triggered after {epoch + 1} epochs")
                    break
        
        # Save final model
        if self.config.SAVE_LAST_MODEL:
            final_path = os.path.join(
                ModelConfig.MODEL_SAVE_PATH,
                'final_model.pth'
            )
            self.save_checkpoint(final_path)
        
        training_time = time.time() - start_time
        print(f"Training completed in {training_time:.2f} seconds")
        
        if self.writer:
            self.writer.close()
        
        return self.training_history
