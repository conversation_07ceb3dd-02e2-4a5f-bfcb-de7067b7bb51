"""Helper utilities for the AI assistant project"""

import os
import random
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import List, Dict, Any


def set_seed(seed=42):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # For deterministic behavior (may impact performance)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def count_parameters(model):
    """Count total and trainable parameters in a model"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total': total_params,
        'trainable': trainable_params,
        'non_trainable': total_params - trainable_params
    }


def format_time(seconds):
    """Format seconds into human readable time"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{int(minutes)}m {seconds:.1f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{int(hours)}h {int(minutes)}m {seconds:.1f}s"


def plot_training_history(history, save_path=None):
    """Plot training history"""
    fig, axes = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot losses
    axes[0].plot(history['train_loss'], label='Train Loss', color='blue')
    if 'val_loss' in history and history['val_loss']:
        axes[0].plot(history['val_loss'], label='Val Loss', color='red')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # Plot learning rate
    if 'learning_rate' in history and history['learning_rate']:
        axes[1].plot(history['learning_rate'], label='Learning Rate', color='green')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Learning Rate')
        axes[1].set_title('Learning Rate Schedule')
        axes[1].legend()
        axes[1].grid(True)
    else:
        axes[1].text(0.5, 0.5, 'No LR data', ha='center', va='center', transform=axes[1].transAxes)
        axes[1].set_title('Learning Rate Schedule')
    
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training history plot saved to {save_path}")
    
    plt.show()


def calculate_perplexity(loss):
    """Calculate perplexity from cross-entropy loss"""
    return torch.exp(torch.tensor(loss)).item()


def get_device_info():
    """Get device information"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    info = {
        'device': str(device),
        'cuda_available': torch.cuda.is_available()
    }
    
    if torch.cuda.is_available():
        info.update({
            'cuda_version': torch.version.cuda,
            'gpu_count': torch.cuda.device_count(),
            'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else None,
            'gpu_memory': torch.cuda.get_device_properties(0).total_memory if torch.cuda.device_count() > 0 else None
        })
    
    return info


def save_model_info(model, save_path):
    """Save model architecture and parameter info"""
    info = {
        'model_class': model.__class__.__name__,
        'parameters': count_parameters(model),
        'architecture': str(model)
    }
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    with open(save_path, 'w') as f:
        f.write(f"Model Class: {info['model_class']}\n")
        f.write(f"Total Parameters: {info['parameters']['total']:,}\n")
        f.write(f"Trainable Parameters: {info['parameters']['trainable']:,}\n")
        f.write(f"Non-trainable Parameters: {info['parameters']['non_trainable']:,}\n")
        f.write("\nModel Architecture:\n")
        f.write(info['architecture'])
    
    print(f"Model info saved to {save_path}")


def create_directory_structure(base_path):
    """Create the project directory structure"""
    directories = [
        'config',
        'src/models',
        'src/training',
        'src/data',
        'src/inference',
        'src/utils',
        'data/raw',
        'data/processed',
        'data/models',
        'scripts',
        'tests',
        'logs'
    ]
    
    for directory in directories:
        full_path = os.path.join(base_path, directory)
        os.makedirs(full_path, exist_ok=True)
    
    print(f"Directory structure created at {base_path}")


def validate_config(config):
    """Validate configuration parameters"""
    errors = []
    
    # Check required attributes
    required_attrs = ['BATCH_SIZE', 'LEARNING_RATE', 'NUM_EPOCHS']
    for attr in required_attrs:
        if not hasattr(config, attr):
            errors.append(f"Missing required attribute: {attr}")
    
    # Check value ranges
    if hasattr(config, 'BATCH_SIZE') and config.BATCH_SIZE <= 0:
        errors.append("BATCH_SIZE must be positive")
    
    if hasattr(config, 'LEARNING_RATE') and config.LEARNING_RATE <= 0:
        errors.append("LEARNING_RATE must be positive")
    
    if hasattr(config, 'NUM_EPOCHS') and config.NUM_EPOCHS <= 0:
        errors.append("NUM_EPOCHS must be positive")
    
    if errors:
        raise ValueError("Configuration validation failed:\n" + "\n".join(errors))
    
    return True


def memory_usage():
    """Get current memory usage"""
    if torch.cuda.is_available():
        return {
            'allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
            'cached': torch.cuda.memory_reserved() / 1024**3,      # GB
            'max_allocated': torch.cuda.max_memory_allocated() / 1024**3  # GB
        }
    return None


def clear_gpu_memory():
    """Clear GPU memory cache"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU memory cache cleared")
