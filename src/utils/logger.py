"""Logging utilities for the AI assistant project"""

import os
import logging
import sys
from datetime import datetime


def setup_logger(name, log_file=None, level=logging.INFO):
    """Setup logger with file and console handlers"""
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_timestamp():
    """Get current timestamp string"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')


class TrainingLogger:
    """Logger specifically for training metrics"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = get_timestamp()
        log_file = os.path.join(log_dir, f"training_{timestamp}.log")
        
        self.logger = setup_logger("training", log_file)
        self.metrics = {}
    
    def log_epoch(self, epoch, train_loss, val_loss=None, lr=None):
        """Log epoch metrics"""
        message = f"Epoch {epoch}: Train Loss = {train_loss:.4f}"
        
        if val_loss is not None:
            message += f", Val Loss = {val_loss:.4f}"
        
        if lr is not None:
            message += f", LR = {lr:.6f}"
        
        self.logger.info(message)
    
    def log_batch(self, epoch, batch, loss, total_batches):
        """Log batch metrics"""
        if batch % 100 == 0:  # Log every 100 batches
            self.logger.info(
                f"Epoch {epoch}, Batch {batch}/{total_batches}: Loss = {loss:.4f}"
            )
    
    def log_model_info(self, model):
        """Log model information"""
        num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        self.logger.info(f"Model parameters: {num_params:,}")
        self.logger.info(f"Model architecture: {model.__class__.__name__}")
    
    def log_config(self, config):
        """Log configuration"""
        self.logger.info("Training Configuration:")
        for key, value in vars(config).items():
            if not key.startswith('_'):
                self.logger.info(f"  {key}: {value}")


class GenerationLogger:
    """Logger for text generation"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = get_timestamp()
        log_file = os.path.join(log_dir, f"generation_{timestamp}.log")
        
        self.logger = setup_logger("generation", log_file)
    
    def log_generation(self, prompt, generated_text, temperature, length):
        """Log text generation"""
        self.logger.info(f"Prompt: '{prompt}'")
        self.logger.info(f"Temperature: {temperature}, Length: {length}")
        self.logger.info(f"Generated: '{generated_text}'")
        self.logger.info("-" * 50)
